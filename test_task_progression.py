#!/usr/bin/env python
import os
import sys
import django

# Add the project directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'prompt_game.settings')
django.setup()

# Force reload of models module to pick up changes
import importlib
import game.models
importlib.reload(game.models)

from game.models import GameSession
from game.all_role_tasks import get_all_role_tasks

def test_task_progression():
    # Reset session to initial state for testing
    session = GameSession.objects.get(id=24)
    session.current_task = 'job_analysis'
    session.role_challenges_completed = 0
    session.save()

    print('=== TESTING SEQUENTIAL TASK DISPLAY FIX ===')
    print(f'Initial state: role={session.current_role}, current_task={session.current_task}, role_challenges_completed={session.role_challenges_completed}')
    print()

    # Test 1: Initial state - should only show task 1
    print('TEST 1: Initial state (task 1 active)')
    game_state = session.to_dict()
    task_messages = [msg for msg in game_state['messages'] if msg.get('is_challenge') and msg.get('task_id')]
    print(f'Task messages shown: {[msg["task_id"] for msg in task_messages]}')
    print('Expected: ["job_analysis"] only')
    print()

    # Test 2: Complete task 1, move to task 2
    print('TEST 2: After completing task 1 (task 2 active)')
    session.role_challenges_completed = 1
    session.current_task = 'customer_service'
    session.save()

    game_state = session.to_dict()
    task_messages = [msg for msg in game_state['messages'] if msg.get('is_challenge') and msg.get('task_id')]
    print(f'Task messages shown: {[msg["task_id"] for msg in task_messages]}')
    print('Expected: ["job_analysis", "customer_service"] only')
    print()

    # Test 3: Complete task 2, move to task 3
    print('TEST 3: After completing task 2 (task 3 active)')
    session.role_challenges_completed = 2
    session.current_task = 'onboarding_checklist'
    session.save()

    game_state = session.to_dict()
    task_messages = [msg for msg in game_state['messages'] if msg.get('is_challenge') and msg.get('task_id')]
    print(f'Task messages shown: {[msg["task_id"] for msg in task_messages]}')
    print('Expected: ["job_analysis", "customer_service", "onboarding_checklist"]')
    print()

    print('=== VERIFICATION: Check all messages in database ===')
    all_messages = session.messages.all().order_by('timestamp')
    task_msg_count = {}
    for msg in all_messages:
        if msg.task_id:
            task_msg_count[msg.task_id] = task_msg_count.get(msg.task_id, 0) + 1

    print(f'Messages in database by task_id: {task_msg_count}')

    # Reset to original state
    session.current_task = 'job_analysis'
    session.role_challenges_completed = 0
    session.save()
    print('\nSession reset to original state for continued testing.')

if __name__ == '__main__':
    test_task_progression()
